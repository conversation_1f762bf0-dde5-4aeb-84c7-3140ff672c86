<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pet Adoption Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .filters {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .filters input, .filters select {
            margin: 5px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .pets-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        .pet-card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        .pet-card:hover {
            transform: translateY(-2px);
        }
        .pet-image {
            width: 100%;
            height: 200px;
            background-color: #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 14px;
        }
        .pet-info {
            padding: 15px;
        }
        .pet-name {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .pet-details {
            color: #666;
            margin-bottom: 10px;
        }
        .pet-description {
            color: #555;
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 10px;
        }
        .pet-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-bottom: 10px;
        }
        .tag {
            background: #e3f2fd;
            color: #1976d2;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        .pet-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 10px;
            border-top: 1px solid #eee;
        }
        .adoption-fee {
            font-weight: bold;
            color: #4caf50;
        }
        .favorite-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #ccc;
        }
        .favorite-btn.active {
            color: #f44336;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .age-badge {
            background: #fff3e0;
            color: #f57c00;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-left: 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🐾 Pet Adoption Center</h1>
        <p>Find your perfect companion</p>
    </div>

    <div class="filters">
        <input type="text" id="searchInput" placeholder="Search pets..." />
        <select id="speciesFilter">
            <option value="">All Species</option>
            <option value="Dog">Dogs</option>
            <option value="Cat">Cats</option>
        </select>
        <select id="ageCategoryFilter">
            <option value="">All Ages</option>
            <option value="Young">Young</option>
            <option value="Adult">Adult</option>
            <option value="Senior">Senior</option>
        </select>
        <select id="genderFilter">
            <option value="">All Genders</option>
            <option value="Male">Male</option>
            <option value="Female">Female</option>
        </select>
        <input type="text" id="cityFilter" placeholder="City" />
        <button onclick="loadPets()">Search</button>
    </div>

    <div id="errorMessage" class="error" style="display: none;"></div>
    <div id="loading" class="loading">Loading pets...</div>
    <div id="petsGrid" class="pets-grid"></div>

    <script>
        const API_BASE = 'http://localhost:5000/api';
        let currentPets = [];

        // Load pets on page load
        document.addEventListener('DOMContentLoaded', loadPets);

        async function loadPets() {
            try {
                showLoading(true);
                hideError();

                const params = new URLSearchParams();
                
                const species = document.getElementById('speciesFilter').value;
                const ageCategory = document.getElementById('ageCategoryFilter').value;
                const gender = document.getElementById('genderFilter').value;
                const city = document.getElementById('cityFilter').value;
                
                if (species) params.append('species', species);
                if (ageCategory) params.append('ageCategory', ageCategory);
                if (gender) params.append('gender', gender);
                if (city) params.append('city', city);

                const response = await fetch(`${API_BASE}/adoption/?${params}`);
                const data = await response.json();

                if (response.ok) {
                    currentPets = data.adoptions;
                    displayPets(currentPets);
                } else {
                    showError(data.message || 'Failed to load pets');
                }
            } catch (error) {
                showError('Network error: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        function displayPets(pets) {
            const grid = document.getElementById('petsGrid');
            
            if (pets.length === 0) {
                grid.innerHTML = '<div class="loading">No pets found matching your criteria.</div>';
                return;
            }

            grid.innerHTML = pets.map(pet => `
                <div class="pet-card" onclick="viewPetDetails('${pet._id}')">
                    <div class="pet-image">
                        ${pet.primaryImage ? `<img src="${pet.primaryImage}" alt="${pet.name}" style="width:100%;height:100%;object-fit:cover;">` : 'No Image Available'}
                    </div>
                    <div class="pet-info">
                        <div class="pet-name">
                            ${pet.name}
                            <span class="age-badge">${pet.age}</span>
                        </div>
                        <div class="pet-details">
                            ${pet.breed} • ${pet.gender} • ${pet.size || 'Size not specified'}
                        </div>
                        <div class="pet-description">
                            ${pet.description.length > 100 ? pet.description.substring(0, 100) + '...' : pet.description}
                        </div>
                        <div class="pet-tags">
                            ${pet.personality.slice(0, 3).map(trait => `<span class="tag">${trait}</span>`).join('')}
                            ${pet.goodWithKids ? '<span class="tag">Good with kids</span>' : ''}
                            ${pet.goodWithDogs ? '<span class="tag">Good with dogs</span>' : ''}
                            ${pet.goodWithCats ? '<span class="tag">Good with cats</span>' : ''}
                        </div>
                        <div class="pet-footer">
                            <div class="adoption-fee">
                                ${pet.adoptionFee ? `$${pet.adoptionFee}` : 'Contact for fee'}
                            </div>
                            <button class="favorite-btn" onclick="toggleFavorite(event, '${pet._id}')" title="Add to favorites">
                                ♡
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        async function viewPetDetails(petId) {
            try {
                const response = await fetch(`${API_BASE}/adoption/${petId}`);
                const data = await response.json();

                if (response.ok) {
                    const pet = data.adoption;
                    alert(`
Pet Details:
Name: ${pet.name}
Species: ${pet.species}
Breed: ${pet.breed}
Age: ${pet.age}
Gender: ${pet.gender}
Location: ${pet.location.city}, ${pet.location.state}
Adoption Fee: $${pet.adoptionFee}
Description: ${pet.description}

Shelter: ${pet.shelter.name}
Phone: ${pet.shelter.phone}
                    `);
                } else {
                    showError('Failed to load pet details');
                }
            } catch (error) {
                showError('Network error: ' + error.message);
            }
        }

        function toggleFavorite(event, petId) {
            event.stopPropagation();
            alert('Favorite functionality requires user authentication. Please implement login first.');
        }

        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        function hideError() {
            document.getElementById('errorMessage').style.display = 'none';
        }

        // Search functionality
        document.getElementById('searchInput').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            if (searchTerm === '') {
                displayPets(currentPets);
                return;
            }

            const filteredPets = currentPets.filter(pet => 
                pet.name.toLowerCase().includes(searchTerm) ||
                pet.breed.toLowerCase().includes(searchTerm) ||
                pet.description.toLowerCase().includes(searchTerm) ||
                pet.personality.some(trait => trait.toLowerCase().includes(searchTerm))
            );
            
            displayPets(filteredPets);
        });
    </script>
</body>
</html>
