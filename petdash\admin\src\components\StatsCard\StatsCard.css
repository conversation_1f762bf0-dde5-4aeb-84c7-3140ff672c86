/* StatsCard - Tailwind to CSS migration */

.statscard-card {
  background: #fff;
  border-radius: 0.75rem;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.statscard-body {
  padding: 0;
}

.statscard-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.statscard-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
}

.statscard-value {
  font-size: 2rem;
  font-weight: bold;
  color: #111827;
  margin-top: 0.5rem;
}

.statscard-change {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.statscard-icon {
  padding: 0.75rem;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.statscard-blue {
  background: #dbeafe;
  color: #2563eb;
}
.statscard-green {
  background: #d1fae5;
  color: #059669;
}
.statscard-purple {
  background: #ede9fe;
  color: #7c3aed;
}
.statscard-orange {
  background: #ffedd5;
  color: #ea580c;
}
.statscard-red {
  background: #fee2e2;
  color: #dc2626;
}

.statscard-loader {
  height: 2rem;
  background: #e5e7eb;
  border-radius: 0.5rem;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.4; }
}
