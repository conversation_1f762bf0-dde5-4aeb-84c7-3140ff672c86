/* UserFilters - Tailwind to CSS migration */

.userfilters-section {
  margin-bottom: 1.5rem;
}

.userfilters-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}
@media (min-width: 768px) {
  .userfilters-grid {
    grid-template-columns: 1fr 1fr 1fr 1fr;
  }
}

.userfilters-label {
  display: block;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.25rem;
}

.userfilters-select {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 1rem;
  color: #111827;
  background: #fff;
}

.userfilters-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
}

.userfilters-btn-clear {
  display: inline-flex;
  align-items: center;
  background: #f3f4f6;
  color: #374151;
  font-size: 0.875rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  padding: 0.25rem 0.75rem;
  cursor: pointer;
  transition: background 0.2s;
}
.userfilters-btn-clear:hover {
  background: #e5e7eb;
}
