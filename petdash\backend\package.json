{"name": "petdesh-backend", "version": "1.0.0", "description": "Backend for Petdesh application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node seed.js", "migrate-addresses": "node migrations/migrateAddresses.js", "test-addresses": "node test-address-system.js", "test-training": "node test-training-system.js", "migrate-roles": "node migrations/migrateRoleSwitching.js", "test-roles": "node test-role-switching.js"}, "dependencies": {"axios": "^1.10.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.3", "multer": "^2.0.1", "nodemailer": "^7.0.5"}, "devDependencies": {"nodemon": "^3.0.2"}}